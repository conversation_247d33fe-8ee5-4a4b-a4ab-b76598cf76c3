# Israeli B2B Invoicing Application - Master Architecture

## Project Overview
- **Type**: B2B SaaS Invoicing Application for Israeli Market
- **Target Launch**: 2 weeks MVP, 1 month testing
- **Tech Stack**: Supabase, Next.js/React Native, iOS (SwiftUI)
- **Business Model**: Free tier (data monetization) + Paid AI tier (99 ILS/month)

## Core Business Requirements

### Supported Document Types
1. **חשבונית מס (Tax Invoice)** - Standard B2B invoice
2. **קבלה (Receipt)** - Payment confirmation
3. **חשבונית מס-קבלה (Tax Invoice-Receipt)** - Combined document
4. **הודעת זיכוי (Credit Note)** - Credit/refund document

### Israeli Law Compliance
- **Sequential Numbering**: Strict sequential numbering per document type, no gaps allowed
- **ITA Integration**: Direct API integration with SHAAM system for allocation numbers
- **VAT Rate**: Standard 18% nationwide
- **Retention**: 7-year document retention requirement
- **Language**: Bilingual support (Hebrew/English)

### User Roles
- **Company Admin**: Full access, can manage users
- **Company User**: Create and view documents
- **Accountant**: Approve/reject expenses, generate reports

## System Architecture

### Database Structure
- **Multi-tenant**: Company-based isolation
- **RLS**: Row-level security on all tables
- **Audit Trail**: Comprehensive logging of all actions

### Key Features
1. **Document Management**
   - Wizard-based creation flow
   - Automatic sequential numbering
   - ITA allocation number integration
   - PDF generation with templates
   - Email/WhatsApp delivery

2. **Expense Management**
   - AI email scanning (paid tier only)
   - Duplicate detection
   - Category classification
   - Accountant approval workflow

3. **Reporting**
   - Bi-monthly VAT reports
   - Export capabilities (PDF, Excel, PCN874)
   - Real-time VAT liability calculation

### Integration Points
1. **ITA SHAAM API**: Document submission and allocation
2. **Email Providers**: Gmail/Outlook OAuth for scanning
3. **OpenAI API**: Receipt/invoice extraction from emails
4. **WhatsApp**: Document sharing via deep links

## Security & Compliance
- **Authentication**: Supabase Auth with email/password
- **Data Residency**: Supabase hosted in EU region
- **Encryption**: At rest and in transit
- **Privacy**: GDPR compliant, Israeli Privacy Law compliant

## Mobile Strategy
- **iOS First**: Native SwiftUI implementation
- **Offline**: No offline support in MVP
- **Features**: Full feature parity with web

## Monetization
- **Free Tier**: Basic features with survey data collection
- **Paid Tier**: 99 ILS/month for AI email scanning
- **Referrals**: Lead generation for loans, insurance, accounting

## Development Phases
1. **Week 1**: Core invoicing, ITA integration, document management
2. **Week 2**: Expenses, reporting, mobile app
3. **Month 1**: Testing, polish, beta launch