{"name": "@fintech/ui", "version": "1.0.0", "description": "Shared UI components and design system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "typescript": "^5.5.3"}}